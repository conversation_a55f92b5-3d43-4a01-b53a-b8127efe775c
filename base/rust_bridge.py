"""
Python bridge to Rust parsing and database operations.

This module provides a Python interface to the Rust implementation
of parsing and database operations for improved performance.
"""

import os
import sys
from typing import List, Tuple, Optional, Dict, Any

try:
    import eterna_rust
    RUST_AVAILABLE = True
except ImportError:
    RUST_AVAILABLE = False
    print("Warning: Rust module not available. Falling back to Python implementation.")


class RustBridge:
    """Bridge class for calling Rust functions from Python."""
    
    def __init__(self):
        self.rust_available = RUST_AVAILABLE
    
    def parse_line(
        self,
        line: str,
        config_type: str,
        object_list_of_names_and_addresses: List[str],
        object_dict_of_addresses_and_names: Dict[str, str],
    ) -> <PERSON>ple[Optional[str], Optional[List[str]]]:
        """
        Parse a single line using Rust implementation.
        
        Args:
            line: The log line to parse
            config_type: Type of configuration (e.g., 'daemon', 'snort')
            object_list_of_names_and_addresses: List of object names and addresses
            object_dict_of_addresses_and_names: Dictionary mapping addresses to names
            
        Returns:
            Tuple of (object_name, parsed_line) or (None, None) if parsing failed
        """
        if not self.rust_available:
            # Fallback to Python implementation
            from .utils_parsers import parse_ln
            from .utils_classes import DaemonConfig  # Import appropriate config
            
            return parse_ln(
                line,
                DaemonConfig,  # This should be dynamic based on config_type
                object_list_of_names_and_addresses,
                object_dict_of_addresses_and_names,
            )
        
        return eterna_rust.rust_parse_line(
            line,
            config_type,
            object_list_of_names_and_addresses,
            object_dict_of_addresses_and_names,
        )
    
    def parse_lines_batch(
        self,
        lines: List[str],
        config_type: str,
        object_list_of_names_and_addresses: List[str],
        object_dict_of_addresses_and_names: Dict[str, str],
    ) -> List[Tuple[str, List[str]]]:
        """
        Parse multiple lines in batch using Rust implementation.
        
        Args:
            lines: List of log lines to parse
            config_type: Type of configuration
            object_list_of_names_and_addresses: List of object names and addresses
            object_dict_of_addresses_and_names: Dictionary mapping addresses to names
            
        Returns:
            List of (object_name, parsed_line) tuples for successfully parsed lines
        """
        if not self.rust_available:
            # Fallback to Python implementation
            results = []
            for line in lines:
                object_name, parsed_line = self.parse_line(
                    line, config_type, object_list_of_names_and_addresses, object_dict_of_addresses_and_names
                )
                if object_name and parsed_line:
                    results.append((object_name, parsed_line))
            return results
        
        return eterna_rust.rust_parse_lines_batch(
            lines,
            config_type,
            object_list_of_names_and_addresses,
            object_dict_of_addresses_and_names,
        )
    
    def process_log_file(
        self,
        file_path: str,
        config_type: str,
        object_list_of_names_and_addresses: List[str],
        object_dict_of_addresses_and_names: Dict[str, str],
    ) -> List[Tuple[str, List[str]]]:
        """
        Process an entire log file using Rust implementation.
        
        Args:
            file_path: Path to the log file
            config_type: Type of configuration
            object_list_of_names_and_addresses: List of object names and addresses
            object_dict_of_addresses_and_names: Dictionary mapping addresses to names
            
        Returns:
            List of (object_name, parsed_line) tuples for successfully parsed lines
        """
        if not self.rust_available:
            # Fallback to Python implementation
            results = []
            with open(file_path, 'r', errors='ignore') as f:
                for line in f:
                    object_name, parsed_line = self.parse_line(
                        line.strip(), config_type, object_list_of_names_and_addresses, object_dict_of_addresses_and_names
                    )
                    if object_name and parsed_line:
                        results.append((object_name, parsed_line))
            return results
        
        return eterna_rust.rust_process_log_file(
            file_path,
            config_type,
            object_list_of_names_and_addresses,
            object_dict_of_addresses_and_names,
        )
    
    def create_database(self, database_name: str) -> None:
        """Create a database using Rust implementation."""
        if not self.rust_available:
            # Fallback to Python implementation
            from MySQLdb import connect
            from .utils_classes import MYSQLConfig
            
            with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'CREATE DATABASE IF NOT EXISTS {database_name};')
            return
        
        eterna_rust.rust_create_database(database_name)
    
    def drop_database(self, database_name: str) -> None:
        """Drop a database using Rust implementation."""
        if not self.rust_available:
            # Fallback to Python implementation
            from MySQLdb import connect
            from .utils_classes import MYSQLConfig
            
            with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'DROP DATABASE IF EXISTS {database_name};')
            return
        
        eterna_rust.rust_drop_database(database_name)
    
    def create_table(self, database_name: str, table_name: str, columns: str) -> None:
        """Create a table using Rust implementation."""
        if not self.rust_available:
            # Fallback to Python implementation
            from MySQLdb import connect
            from .utils_classes import MYSQLConfig
            
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'CREATE TABLE {table_name} ({columns});')
            return
        
        eterna_rust.rust_create_table(database_name, table_name, columns)
    
    def write_infiles_and_load(
        self,
        rows: List[List[str]],
        database_name: str,
        table_name: str,
    ) -> None:
        """
        Write rows to infiles and load them into the database using Rust implementation.
        
        Args:
            rows: List of rows, where each row is a list of strings
            database_name: Name of the target database
            table_name: Name of the target table
        """
        if not self.rust_available:
            # Fallback to Python implementation
            from .utils import create_path_of_infile, get_no_of_infiles
            from .utils_classes import MYSQLConfig, DaemonConfig
            from MySQLdb import connect
            from os import remove
            from multiprocessing import Process
            
            # This is a simplified fallback - the full Python implementation
            # would be more complex with chunking and parallel processing
            if not rows:
                return
                
            # Create a single infile for simplicity
            infile_path = create_path_of_infile(database_name, table_name)
            
            with open(infile_path, 'w') as f:
                for i, row in enumerate(rows, 1):
                    formatted_row = f'{MYSQLConfig.TERMINATED_BY.value}'.join(
                        f'{MYSQLConfig.ENCLOSED_BY.value}{cell}{MYSQLConfig.ENCLOSED_BY.value}'
                        for cell in [str(i)] + row
                    )
                    f.write(formatted_row + '\n')
            
            # Load into database
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute('SET UNIQUE_CHECKS=0;')
                    cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                    cur.execute('START TRANSACTION;')
                    cur.execute(f'''
                        {MYSQLConfig.get_infile_statement()} "{infile_path}"
                        INTO TABLE {table_name}
                        FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                        ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                        LINES TERMINATED BY "\\n"
                        (ID,{DaemonConfig.DB_KEYS.value})
                    ;''')
                    conn.commit()
            
            # Cleanup
            remove(infile_path)
            return
        
        eterna_rust.rust_write_infiles_and_load(rows, database_name, table_name)
    
    # Utility functions
    def get_today_ymd(self) -> str:
        """Get today's date in YYYY-MM-DD format."""
        if not self.rust_available:
            from .utils import get_today_ymd
            return get_today_ymd()
        return eterna_rust.rust_get_today_ymd()
    
    def create_name_of_database(self, slug: str, ymd: str, object_name: str) -> str:
        """Create database name from components."""
        if not self.rust_available:
            from .utils import create_name_of_database
            return create_name_of_database(slug, ymd, object_name)
        return eterna_rust.rust_create_name_of_database(slug, ymd, object_name)
    
    def is_invalid_log_date(self, log_date: str, today_ymd: str) -> bool:
        """Check if log date is invalid."""
        if not self.rust_available:
            from .utils import is_invalid_log_date
            return is_invalid_log_date(log_date, today_ymd)
        return eterna_rust.rust_is_invalid_log_date(log_date, today_ymd)
    
    def get_date_of_source_log(self, log_path: str) -> str:
        """Extract date from log file path."""
        if not self.rust_available:
            from .utils import get_date_of_source_log
            return get_date_of_source_log(log_path)
        return eterna_rust.rust_get_date_of_source_log(log_path)


# Global instance
rust_bridge = RustBridge()
