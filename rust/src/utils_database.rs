use std::collections::HashMap;
use std::env;
use mysql::*;
use mysql::prelude::*;

// Database connection and operations using MySQL

pub struct DatabaseConnection {
    pool: Pool,
}

impl DatabaseConnection {
    pub fn new(host: &str, user: &str, password: &str, database: Option<&str>) -> Result<Self, mysql::Error> {
        let mut builder = OptsBuilder::new()
            .ip_or_hostname(Some(host))
            .user(Some(user))
            .pass(Some(password));

        if let Some(db) = database {
            builder = builder.db_name(Some(db));
        }

        let pool = Pool::new(builder)?;
        Ok(Self { pool })
    }

    pub fn from_master_creds() -> Result<Self, Box<dyn std::error::Error>> {
        let host = env::var("MYSQL_HOST")?;
        let user = env::var("MYSQL_MASTER")?;
        let password = env::var("MYSQL_MASTER_PASSWD")?;

        Ok(Self::new(&host, &user, &password, None)?)
    }

    pub fn with_database(&self, database: &str) -> Result<Self, mysql::Error> {
        // Create a new connection with the specified database
        let host = env::var("MYSQL_HOST").unwrap_or_default();
        let user = env::var("MYSQL_MASTER").unwrap_or_default();
        let password = env::var("MYSQL_MASTER_PASSWD").unwrap_or_default();

        Self::new(&host, &user, &password, Some(database))
    }

    pub fn get_connection(&self) -> Result<PooledConn, mysql::Error> {
        self.pool.get_conn()
    }
}

pub struct DatabaseOperations;

impl DatabaseOperations {
    pub fn create_database(conn: &DatabaseConnection, database_name: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut connection = conn.get_connection()?;
        let query = format!("CREATE DATABASE IF NOT EXISTS {}", database_name);
        connection.query_drop(query)?;
        Ok(())
    }

    pub fn drop_database(conn: &DatabaseConnection, database_name: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut connection = conn.get_connection()?;
        let query = format!("DROP DATABASE IF EXISTS {}", database_name);
        connection.query_drop(query)?;
        Ok(())
    }

    pub fn create_table(conn: &DatabaseConnection, table_name: &str, columns: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut connection = conn.get_connection()?;
        let query = format!("CREATE TABLE {} ({})", table_name, columns);
        connection.query_drop(query)?;
        Ok(())
    }

    pub fn load_data_infile(
        conn: &DatabaseConnection,
        infile_path: &str,
        table_name: &str,
        terminated_by: &str,
        enclosed_by: &str,
        keys: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut connection = conn.get_connection()?;
        let query = format_infile_query(infile_path, table_name, terminated_by, enclosed_by, keys);
        connection.query_drop(query)?;
        Ok(())
    }

    pub fn execute_query(conn: &DatabaseConnection, query: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut connection = conn.get_connection()?;
        connection.query_drop(query)?;
        Ok(())
    }

    pub fn start_transaction(conn: &DatabaseConnection) -> Result<(), Box<dyn std::error::Error>> {
        Self::execute_query(conn, "START TRANSACTION")
    }

    pub fn commit(conn: &DatabaseConnection) -> Result<(), Box<dyn std::error::Error>> {
        Self::execute_query(conn, "COMMIT")
    }

    pub fn set_unique_checks(conn: &DatabaseConnection, enabled: bool) -> Result<(), Box<dyn std::error::Error>> {
        let value = if enabled { "1" } else { "0" };
        Self::execute_query(conn, &format!("SET UNIQUE_CHECKS={}", value))
    }

    pub fn set_foreign_key_checks(conn: &DatabaseConnection, enabled: bool) -> Result<(), Box<dyn std::error::Error>> {
        let value = if enabled { "1" } else { "0" };
        Self::execute_query(conn, &format!("SET FOREIGN_KEY_CHECKS={}", value))
    }

    pub fn execute_batch_operations<F>(conn: &DatabaseConnection, operations: F) -> Result<(), Box<dyn std::error::Error>>
    where
        F: FnOnce(&mut PooledConn) -> Result<(), Box<dyn std::error::Error>>,
    {
        let mut connection = conn.get_connection()?;

        // Start transaction
        connection.query_drop("START TRANSACTION")?;

        // Execute operations
        match operations(&mut connection) {
            Ok(_) => {
                connection.query_drop("COMMIT")?;
                Ok(())
            }
            Err(e) => {
                connection.query_drop("ROLLBACK")?;
                Err(e)
            }
        }
    }
}

// Utility functions for database operations
pub fn get_infile_statement() -> &'static str {
    // Check DEBUG environment variable to determine INFILE statement type
    match env::var("DEBUG").as_deref() {
        Ok("True") | Ok("true") | Ok("1") => "LOAD DATA LOCAL INFILE",
        _ => "LOAD DATA INFILE",
    }
}

pub fn format_infile_query(
    infile_path: &str,
    table_name: &str,
    terminated_by: &str,
    enclosed_by: &str,
    keys: &str,
) -> String {
    format!(
        r#"{} "{}"
        INTO TABLE {}
        FIELDS TERMINATED BY "{}"
        ENCLOSED BY '{}'
        LINES TERMINATED BY "\n"
        (ID,{});"#,
        get_infile_statement(),
        infile_path,
        table_name,
        terminated_by,
        enclosed_by,
        keys
    )
}

// Batch processing utilities
pub fn evenly_sized_batches(total_length: usize, len_of_each_batch: usize) -> Vec<std::ops::Range<usize>> {
    // Generate evenly sized batches from a given total length
    let mut batches = Vec::new();
    let mut start = 0;
    
    while start < total_length {
        let end = std::cmp::min(start + len_of_each_batch, total_length);
        batches.push(start..end);
        start = end;
    }
    
    batches
}
