use chrono::{
    NaiveDate,
    NaiveTime,
    Datelike,
    Local,
};
use regex::Regex;
use std::env;
use std::fs;
use std::path::Path;

use crate::utils_classes::MYSQLConfig;


pub fn normalize_date(date: &str) -> String {
    // __HAS_TEST__

    // first try parsing 2-digit year format with manual fix
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%y") {
        let year = parsed.year();
        let fixed_year = if year < 100 {
            if year >= 70 { 1900 + year } else { 2000 + year }
        } else {
            year
        };

        if let Some(fixed_date) = NaiveDate::from_ymd_opt(fixed_year, parsed.month(), parsed.day()) {
            return fixed_date.format("%Y-%m-%d").to_string();
        }
    }

    // then try parsing 4-digit year normally
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%Y") {
        return parsed.format("%Y-%m-%d").to_string();
    }

    // if all formats fail,
    // return the original date string
    date.to_string()
}

pub fn get_today_ymd() -> String {
    // Returns the current date in ISO 8601 format (YYYY-MM-DD)
    Local::now().format("%Y-%m-%d").to_string()
}

pub fn dash_to_underscore(string: &str) -> String {
    // Converts all dashes ('-') in the given string to underscores ('_')
    string.replace('-', "_")
}

pub fn underscore_to_dash(string: &str) -> String {
    // Converts all underscores ('_') in the given string to dashes ('-')
    string.replace('_', "-")
}

pub fn get_date_of_source_log(log_path: &str) -> String {
    // Extracts the date from the log file name
    // Expected format: 'YYYY-MM-DD--Day.log'
    let path = Path::new(log_path);
    if let Some(filename) = path.file_name() {
        if let Some(filename_str) = filename.to_str() {
            // Remove '--Day.log' part and return only the 'YYYY-MM-DD' part
            let re = Regex::new(r"--.*$").unwrap();
            return re.replace(filename_str, "").to_string();
        }
    }
    String::new()
}

pub fn is_invalid_log_date(log_date: &str, today_ymd: &str) -> bool {
    // Check if log date is invalid (same as today or in the future)
    log_date == today_ymd || log_date > today_ymd
}

pub fn get_no_of_infiles(length: usize) -> usize {
    // Calculate the number of input files needed based on the given length
    if length == 0 {
        return 0;
    }

    let chunk_size = match MYSQLConfig::INFILE_CHUNKSIZE.value() {
        crate::utils_classes::MYSQLValue::Int(size) => size as usize,
        _ => 5_000_000, // default fallback
    };

    if length <= chunk_size {
        return 1;
    }

    let mut no_of_chunks = length / chunk_size;
    if length % chunk_size != 0 {
        no_of_chunks += 1;
    }

    no_of_chunks
}

pub fn create_path_of_infile(database_name: &str, table_name: &str, chunk_number: Option<usize>) -> String {
    // Generate the file path for an infile based on the database name, table name, and optional chunk number
    let suffix = if let Some(chunk) = chunk_number {
        format!("__chunk_{}", chunk)
    } else {
        String::new()
    };

    format!("/tmp/infile__{}__{}{}.csv", database_name, table_name, suffix)
}

pub fn create_name_of_database(slug: &str, ymd: &str, object_name: &str) -> String {
    // Generate the name of a database based on the provided slug, date, and object name
    let non_dated_databases_value = MYSQLConfig::NON_DATED_DATABASES.value();
    let non_dated_databases = match non_dated_databases_value {
        crate::utils_classes::MYSQLValue::List(ref list) => list,
        _ => return slug.to_string(), // fallback
    };

    if non_dated_databases.contains(&slug.to_string()) {
        return slug.to_string();
    }

    let separator_value = MYSQLConfig::DB_NAME_SEPARATOR.value();
    let separator = match separator_value {
        crate::utils_classes::MYSQLValue::Str(ref sep) => sep,
        _ => "__", // fallback
    };

    if object_name.is_empty() {
        format!("{}{}{}", slug, separator, dash_to_underscore(ymd))
    } else {
        format!("{}{}{}{}{}", slug, separator, dash_to_underscore(object_name), separator, dash_to_underscore(ymd))
    }
}


pub fn normalize_dns_question_name(url: &str) -> String {
    // __HAS_TEST__

    let re = Regex::new(r"\([0-9]+\)").unwrap();
    let normalized = re.replace_all(url, ".");
    normalized.trim_matches('.').to_string()
}


pub fn normalize_time(time: &str) -> String {
    // __HAS_TEST__

    // try parsing with 12-hour format with AM/PM (%I:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%I:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // try parsing with 24-hour format with AM/PM (%H:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%H:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // if all formats fail,
    // return the original time string
    time.to_string()
}

// ...
