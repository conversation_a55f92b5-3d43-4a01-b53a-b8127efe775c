use pyo3::prelude::*;
use pyo3::types::{<PERSON><PERSON><PERSON><PERSON>, Py<PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, PyModule};
use std::collections::HashMap;

mod snort_classifications;
mod windows_server_audit_events;
mod test_utils;
mod utils;
mod utils_classes;
mod utils_patterns;
mod utils_parsers;
mod rahavard;
mod utils_database;
mod file_processing;

use utils_parsers::parse_ln;
use utils_classes::{DaemonConfig, MYSQLConfig};
use utils_database::{DatabaseConnection, DatabaseOperations};
use file_processing::FileProcessor;

// Python wrapper for parsing functions
#[pyfunction]
fn rust_parse_line(
    line: &str,
    config_type: &str,
    object_list_of_names_and_addresses: Vec<String>,
    object_dict_of_addresses_and_names: HashMap<String, String>,
) -> PyResult<(Option<String>, Option<Vec<String>>)> {
    // Convert config_type string to appropriate config enum
    let result = match config_type {
        "daemon" => parse_ln(
            line,
            utils_classes::ConfigType::Daemon,
            &object_list_of_names_and_addresses,
            &object_dict_of_addresses_and_names,
        ),
        // Add other config types as needed
        _ => (None, None),
    };
    
    Ok(result)
}

// Python wrapper for batch parsing
#[pyfunction]
fn rust_parse_lines_batch(
    lines: Vec<String>,
    config_type: &str,
    object_list_of_names_and_addresses: Vec<String>,
    object_dict_of_addresses_and_names: HashMap<String, String>,
) -> PyResult<Vec<(String, Vec<String>)>> {
    let mut results = Vec::new();
    
    for line in lines {
        let (object_name, parsed_line) = rust_parse_line(
            &line,
            config_type,
            object_list_of_names_and_addresses.clone(),
            object_dict_of_addresses_and_names.clone(),
        )?;
        
        if let (Some(obj_name), Some(parsed)) = (object_name, parsed_line) {
            results.push((obj_name, parsed));
        }
    }
    
    Ok(results)
}

// Python wrapper for file processing
#[pyfunction]
fn rust_process_log_file(
    file_path: &str,
    config_type: &str,
    object_list_of_names_and_addresses: Vec<String>,
    object_dict_of_addresses_and_names: HashMap<String, String>,
) -> PyResult<Vec<(String, Vec<String>)>> {
    let results = FileProcessor::process_log_file_lines(file_path, |line| {
        rust_parse_line(
            line,
            config_type,
            object_list_of_names_and_addresses.clone(),
            object_dict_of_addresses_and_names.clone(),
        ).unwrap_or((None, None))
    });
    
    match results {
        Ok(parsed_lines) => Ok(parsed_lines),
        Err(e) => Err(PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("File processing error: {}", e))),
    }
}

// Python wrapper for database operations
#[pyfunction]
fn rust_create_database(database_name: &str) -> PyResult<()> {
    let conn = DatabaseConnection::from_master_creds()
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Connection error: {}", e)))?;
    
    DatabaseOperations::create_database(&conn, database_name)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Database creation error: {}", e)))?;
    
    Ok(())
}

#[pyfunction]
fn rust_drop_database(database_name: &str) -> PyResult<()> {
    let conn = DatabaseConnection::from_master_creds()
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Connection error: {}", e)))?;
    
    DatabaseOperations::drop_database(&conn, database_name)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Database drop error: {}", e)))?;
    
    Ok(())
}

#[pyfunction]
fn rust_create_table(database_name: &str, table_name: &str, columns: &str) -> PyResult<()> {
    let conn = DatabaseConnection::from_master_creds()
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Connection error: {}", e)))?;
    
    let conn_with_db = conn.with_database(database_name)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Database connection error: {}", e)))?;
    
    DatabaseOperations::create_table(&conn_with_db, table_name, columns)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Table creation error: {}", e)))?;
    
    Ok(())
}

#[pyfunction]
fn rust_write_infiles_and_load(
    rows: Vec<Vec<String>>,
    database_name: &str,
    table_name: &str,
) -> PyResult<()> {
    if rows.is_empty() {
        return Ok(());
    }

    let chunk_size = FileProcessor::get_chunk_size();
    
    // Create infiles
    let infile_paths = FileProcessor::create_infiles_for_rows(&rows, database_name, table_name, chunk_size)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("Infile creation error: {}", e)))?;
    
    // Load data into database
    let conn = DatabaseConnection::from_master_creds()
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Connection error: {}", e)))?;
    
    let conn_with_db = conn.with_database(database_name)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Database connection error: {}", e)))?;
    
    // Get MySQL configuration values
    let terminated_by_value = MYSQLConfig::TERMINATED_BY.value();
    let terminated_by = match terminated_by_value {
        utils_classes::MYSQLValue::Str(ref s) => s.as_str(),
        _ => "-*@*-",
    };

    let enclosed_by_value = MYSQLConfig::ENCLOSED_BY.value();
    let enclosed_by = match enclosed_by_value {
        utils_classes::MYSQLValue::Str(ref s) => s.as_str(),
        _ => "",
    };

    let db_keys_value = DaemonConfig::DB_KEYS.value();
    let db_keys = match db_keys_value {
        utils_classes::MYSQLValue::Str(ref s) => s.as_str(),
        _ => "",
    };
    
    // Load each infile
    for infile_path in &infile_paths {
        DatabaseOperations::load_data_infile(
            &conn_with_db,
            infile_path,
            table_name,
            terminated_by,
            enclosed_by,
            db_keys,
        ).map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Data loading error: {}", e)))?;
    }
    
    // Cleanup infiles
    FileProcessor::cleanup_infiles(&infile_paths)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyIOError, _>(format!("Cleanup error: {}", e)))?;
    
    Ok(())
}

// Utility functions
#[pyfunction]
fn rust_get_today_ymd() -> PyResult<String> {
    Ok(utils::get_today_ymd())
}

#[pyfunction]
fn rust_create_name_of_database(slug: &str, ymd: &str, object_name: &str) -> PyResult<String> {
    Ok(utils::create_name_of_database(slug, ymd, object_name))
}

#[pyfunction]
fn rust_is_invalid_log_date(log_date: &str, today_ymd: &str) -> PyResult<bool> {
    Ok(utils::is_invalid_log_date(log_date, today_ymd))
}

#[pyfunction]
fn rust_get_date_of_source_log(log_path: &str) -> PyResult<String> {
    Ok(utils::get_date_of_source_log(log_path))
}

// Module definition
#[pymodule]
fn eterna_rust(m: &Bound<'_, PyModule>) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(rust_parse_line, m)?)?;
    m.add_function(wrap_pyfunction!(rust_parse_lines_batch, m)?)?;
    m.add_function(wrap_pyfunction!(rust_process_log_file, m)?)?;
    m.add_function(wrap_pyfunction!(rust_create_database, m)?)?;
    m.add_function(wrap_pyfunction!(rust_drop_database, m)?)?;
    m.add_function(wrap_pyfunction!(rust_create_table, m)?)?;
    m.add_function(wrap_pyfunction!(rust_write_infiles_and_load, m)?)?;
    m.add_function(wrap_pyfunction!(rust_get_today_ymd, m)?)?;
    m.add_function(wrap_pyfunction!(rust_create_name_of_database, m)?)?;
    m.add_function(wrap_pyfunction!(rust_is_invalid_log_date, m)?)?;
    m.add_function(wrap_pyfunction!(rust_get_date_of_source_log, m)?)?;
    Ok(())
}
