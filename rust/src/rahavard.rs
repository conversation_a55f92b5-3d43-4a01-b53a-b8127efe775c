use std::fs;
use std::path::Path;
use regex::Regex;
use std::env;

pub fn get_list_of_files(directory: &str, extension: &str) -> Vec<String> {
    // Get a list of files in a directory with a specific extension, sorted naturally
    if !Path::new(directory).exists() {
        return Vec::new();
    }

    let mut files = Vec::new();
    
    if let Ok(entries) = fs::read_dir(directory) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.is_file() {
                if let Some(file_name) = path.file_name() {
                    if let Some(file_name_str) = file_name.to_str() {
                        if file_name_str.ends_with(&format!(".{}", extension)) {
                            if let Some(abs_path) = path.to_str() {
                                files.push(abs_path.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    // Sort naturally (basic implementation - could be improved with natord crate)
    files.sort();
    files
}

pub fn is_ymd(string: &str) -> bool {
    // Check if a given string matches the Year-Month-Day (YMD) format
    // YMD_REGEX pattern: YYYY-MM-DD
    let ymd_regex = Regex::new(r"^\d{4}-\d{2}-\d{2}$").unwrap();
    ymd_regex.is_match(string)
}

pub fn convert_second(seconds: f64, verbose: bool) -> String {
    // Convert a given number of seconds into a human-readable string format
    if seconds == 0.0 {
        return if verbose { "0".to_string() } else { "0:00".to_string() };
    }

    if seconds < 1.0 {
        return if verbose { "~0".to_string() } else { "~0:00".to_string() };
    }

    let ss = (seconds as u64 % 60) as u32;
    let mi = ((seconds as u64 / 60) % 60) as u32;
    let hh = ((seconds as u64 / 3600) % 24) as u32;
    let dd = ((seconds as u64 / 3600 / 24) % 30) as u32;
    let mo = ((seconds as u64 / 3600 / 24 / 30) % 12) as u32;
    let yy = (seconds as u64 / 3600 / 24 / 30 / 12) as u32;

    if verbose {
        let mut parts = Vec::new();
        
        if yy > 0 {
            parts.push(format!("{} year{}", yy, if yy == 1 { "" } else { "s" }));
        }
        if mo > 0 {
            parts.push(format!("{} month{}", mo, if mo == 1 { "" } else { "s" }));
        }
        if dd > 0 {
            parts.push(format!("{} day{}", dd, if dd == 1 { "" } else { "s" }));
        }
        if hh > 0 {
            parts.push(format!("{} hr{}", hh, if hh == 1 { "" } else { "s" }));
        }
        if mi > 0 {
            parts.push(format!("{} min{}", mi, if mi == 1 { "" } else { "s" }));
        }
        if ss > 0 {
            parts.push(format!("{} sec{}", ss, if ss == 1 { "" } else { "s" }));
        }

        if parts.is_empty() {
            return "0".to_string();
        }

        if parts.len() == 1 {
            return parts[0].clone();
        }

        let last = parts.pop().unwrap();
        format!("{} and {}", parts.join(", "), last)
    } else {
        // Non-verbose format
        if yy == 0 && mo == 0 && dd == 0 {
            format!("{:02}:{:02}:{:02}", hh, mi, ss)
        } else if yy == 0 && mo == 0 {
            format!("{:02}:{:02}:{:02}:{:02}", dd, hh, mi, ss)
        } else if yy == 0 {
            format!("{:02}:{:02}:{:02}:{:02}:{:02}", mo, dd, hh, mi, ss)
        } else {
            format!("{:02}:{:02}:{:02}:{:02}:{:02}:{:02}", yy, mo, dd, hh, mi, ss)
        }
    }
}

pub fn get_command(full_path: &str, drop_extension: bool) -> String {
    // Extracts the command name from a given full path of a Django custom command
    let path = Path::new(full_path);
    if let Some(filename) = path.file_name() {
        if let Some(filename_str) = filename.to_str() {
            if drop_extension {
                if let Some(stem) = path.file_stem() {
                    if let Some(stem_str) = stem.to_str() {
                        return stem_str.to_string();
                    }
                }
            }
            return filename_str.to_string();
        }
    }
    String::new()
}

pub fn to_tilda(text: &str) -> String {
    // Replaces the home directory path in the given text with a tilde (~)
    if let Ok(home) = env::var("HOME") {
        text.replace(&home, "~")
    } else {
        text.to_string()
    }
}

// Note: keyboard_interrupt_handler, save_log, and add_yearmonthday_force are Django-specific
// and will be handled in the Python-Rust bridge interface
