use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Write};
use std::path::Path;
use crate::utils_classes::{MYSQLConfig, MYSQLValue};
use crate::utils::create_path_of_infile;

pub struct FileProcessor;

impl FileProcessor {
    pub fn write_infile_chunk(
        rows: &[Vec<String>],
        start_index: usize,
        end_index: usize,
        infile_path: &str,
        row_id_start: usize,
    ) -> Result<(), std::io::Error> {
        // Write a chunk of rows to an infile
        let mut file = File::create(infile_path)?;
        
        let terminated_by_value = MYSQLConfig::TERMINATED_BY.value();
        let terminated_by = match terminated_by_value {
            MYSQLValue::Str(ref s) => s.as_str(),
            _ => "-*@*-", // fallback
        };

        let enclosed_by_value = MYSQLConfig::ENCLOSED_BY.value();
        let enclosed_by = match enclosed_by_value {
            MYSQLValue::Str(ref s) => s.as_str(),
            _ => "", // fallback
        };

        let mut row_id = row_id_start;
        
        for row in &rows[start_index..end_index.min(rows.len())] {
            row_id += 1;
            
            // Create the formatted row with ID
            let mut formatted_cells = vec![format!("{}{}{}", enclosed_by, row_id, enclosed_by)];
            
            for cell in row {
                formatted_cells.push(format!("{}{}{}", enclosed_by, cell, enclosed_by));
            }
            
            let line = format!("{}\n", formatted_cells.join(terminated_by));
            file.write_all(line.as_bytes())?;
        }
        
        Ok(())
    }

    pub fn process_log_file_lines<F>(
        file_path: &str,
        mut line_processor: F,
    ) -> Result<Vec<(String, Vec<String>)>, std::io::Error>
    where
        F: FnMut(&str) -> (Option<String>, Option<Vec<String>>),
    {
        // Process lines from a log file and return valid parsed lines
        let file = File::open(file_path)?;
        let reader = BufReader::new(file);
        let mut results = Vec::new();

        for line in reader.lines() {
            let line = line?;
            let (object_name, parsed_line) = line_processor(&line);
            
            if let (Some(obj_name), Some(parsed)) = (object_name, parsed_line) {
                results.push((obj_name, parsed));
            }
        }

        Ok(results)
    }

    pub fn create_infiles_for_rows(
        rows: &[Vec<String>],
        database_name: &str,
        table_name: &str,
        chunk_size: usize,
    ) -> Result<Vec<String>, std::io::Error> {
        // Create multiple infiles for large datasets
        let mut infile_paths = Vec::new();
        
        if rows.is_empty() {
            return Ok(infile_paths);
        }

        let total_chunks = (rows.len() + chunk_size - 1) / chunk_size; // Ceiling division
        
        for chunk_index in 0..total_chunks {
            let start_index = chunk_index * chunk_size;
            let end_index = ((chunk_index + 1) * chunk_size).min(rows.len());
            
            let infile_path = create_path_of_infile(
                database_name,
                table_name,
                Some(chunk_index + 1),
            );
            
            Self::write_infile_chunk(
                rows,
                start_index,
                end_index,
                &infile_path,
                start_index,
            )?;
            
            infile_paths.push(infile_path);
        }

        Ok(infile_paths)
    }

    pub fn cleanup_infiles(infile_paths: &[String]) -> Result<(), std::io::Error> {
        // Remove temporary infiles after processing
        for path in infile_paths {
            if Path::new(path).exists() {
                std::fs::remove_file(path)?;
            }
        }
        Ok(())
    }

    pub fn get_chunk_size() -> usize {
        // Get the configured chunk size for infiles
        match MYSQLConfig::INFILE_CHUNKSIZE.value() {
            MYSQLValue::Int(size) => size as usize,
            _ => 5_000_000, // default fallback
        }
    }

    pub fn batch_process_ranges(total_length: usize, batch_size: usize) -> Vec<std::ops::Range<usize>> {
        // Create batch ranges for parallel processing
        let mut batches = Vec::new();
        let mut start = 0;
        
        while start < total_length {
            let end = (start + batch_size).min(total_length);
            batches.push(start..end);
            start = end;
        }
        
        batches
    }
}

// Utility functions for file operations
pub fn ensure_directory_exists(path: &str) -> Result<(), std::io::Error> {
    let path = Path::new(path);
    if let Some(parent) = path.parent() {
        std::fs::create_dir_all(parent)?;
    }
    Ok(())
}

pub fn remove_file_if_exists(path: &str) -> Result<(), std::io::Error> {
    let path = Path::new(path);
    if path.exists() {
        std::fs::remove_file(path)?;
    }
    Ok(())
}

pub fn get_file_size(path: &str) -> Result<u64, std::io::Error> {
    let metadata = std::fs::metadata(path)?;
    Ok(metadata.len())
}

// Parallel processing utilities
pub fn split_work_into_batches<T: Clone>(items: Vec<T>, num_batches: usize) -> Vec<Vec<T>> {
    if items.is_empty() || num_batches == 0 {
        return vec![items];
    }

    let chunk_size = (items.len() + num_batches - 1) / num_batches; // Ceiling division
    items.chunks(chunk_size).map(|chunk| chunk.to_vec()).collect()
}
