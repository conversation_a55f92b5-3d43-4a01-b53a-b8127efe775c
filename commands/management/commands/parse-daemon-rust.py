'''
NOTE this parser uses quick parsing method with Rust acceleration
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from functools import partial
from multiprocessing import Pool, Process
from operator import itemgetter
from os import path, makedirs, remove
from shutil import rmtree
from signal import SIGINT, signal
from time import perf_counter
from typing import List, Optional, Tuple

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    colorize,
    convert_second,
    get_command,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_classes import (
    DaemonParser,
    DaemonConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils_parsers import (
    parse_ln,
)

from base.utils import (
    create_name_of_database,
    create_path_of_infile,
    end_of_command_msg,
    evenly_sized_batches,
    filter_list,
    get_date_of_source_log,
    get_no_of_infiles,
    get_today_ymd,
    is_invalid_log_date,
    separator,
    source_log_info_line,
)

from base.models import Sensor

# Import the Rust bridge
from base.rust_bridge import rust_bridge


signal(SIGINT, keyboard_interrupt_handler)


sensor_list_of_names               = Sensor.get_list_of_names()
sensor_list_of_names_and_addresses = Sensor.get_list_of_names_and_addresses()
sensor_dict_of_addresses_and_names = Sensor.get_dict_of_addresses_and_names()


def parse_line_rust(ln: str, already_accomplished: List[str]) -> Tuple[Optional[str], Optional[str]]:
    '''
    Parses a given line using Rust implementation and extracts relevant information.

    Args:
        ln (str): The input line to be parsed.
        already_accomplished (List[str]): A list of object names that have already been processed.

    Returns:
        Tuple[Optional[str], Optional[str]]: A tuple containing the object name and the parsed line.
        If the line is invalid or the object has already been processed, returns (None, None).
    '''

    _sensor_name, _parsed_ln = rust_bridge.parse_line(
        ln.strip(),
        'daemon',  # config type for DaemonConfig
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
    )

    if _sensor_name in already_accomplished:
        return (None, None)

    return (_sensor_name, _parsed_ln)


class Command(BaseCommand):
    help = f'Parse {DaemonConfig.TITLE.value} (Rust-accelerated)'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser, for_mysql=False)

    def handle(self, *args, **kwargs):
        year_months     = kwargs.get('year_months')
        year_month_days = kwargs.get('year_month_days')

        start_year_month     = kwargs.get('start_year_month')
        start_year_month_day = kwargs.get('start_year_month_day')

        end_year_month     = kwargs.get('end_year_month')
        end_year_month_day = kwargs.get('end_year_month_day')

        force = kwargs.get('force')

        if year_months:     year_months     = natsorted(set(year_months))
        if year_month_days: year_month_days = natsorted(set(year_month_days))

        if start_year_month and end_year_month:
            ## make sure start_year_month precedes end_year_month in time
            if start_year_month >= end_year_month:
                end_year_month = None

        if start_year_month_day and end_year_month_day:
            ## make sure start_year_month_day precedes end_year_month_day in time
            if start_year_month_day >= end_year_month_day:
                end_year_month_day = None

        command = get_command(__file__)

        today_ymd = rust_bridge.get_today_ymd()

        ## get list of source logs
        source_logs = get_list_of_files(directory=settings.LOGS_DIR, extension='log')

        ## filter source logs
        source_logs = filter_list(
            list_of_items=source_logs,

            year_months=year_months,
            year_month_days=year_month_days,

            start_year_month=start_year_month,
            start_year_month_day=start_year_month_day,

            end_year_month=end_year_month,
            end_year_month_day=end_year_month_day,
        )

        if not source_logs:
            abort(self, f'{command}: no source logs found')

        print(f'{len(source_logs):,} source logs found')

        for source_log_index, source_log in enumerate(source_logs, start=1):
            ## source_log may have been removed
            ## since the start of this command
            if not path.exists(source_log):
                print(colorize(self, 'error', f'{to_tilda(source_log)} does not exist. skipping parsing'))
                continue

            source_log_start = perf_counter()

            log_date = rust_bridge.get_date_of_source_log(source_log)

            if rust_bridge.is_invalid_log_date(log_date, today_ymd):
                continue

            ## find object names (i.e. names of windowsservers, routers, etc.)
            ## that have already been accomplished
            ## for this specific log_date
            already_accomplished = []
            if not force:
                for _ in sensor_list_of_names:
                    if path.exists(f'{DaemonConfig.get_logs_parsed_dir()}/{_}/{log_date}/{log_date}-accomplished.log'):
                        already_accomplished.append(_)

                if len(already_accomplished) == len(sensor_list_of_names):
                    print(colorize(self, 'already_parsed', f'{command}: {log_date} all sensors already parsed, skipping'))
                    continue

            ## create dictionary of instances
            sensor_names_and_instances = {
                _: DaemonParser(slug=DaemonConfig.SLUG.value, ymd=log_date, object_name=_)
                for _ in sensor_list_of_names
            }

            print(source_log_info_line(source_log, source_log_index, len(source_logs)))

            # Use Rust for parsing the entire file
            print('parsing with Rust...')
            parse_start = perf_counter()
            
            try:
                # Process the entire file with Rust
                parsed_results = rust_bridge.process_log_file(
                    source_log,
                    'daemon',
                    sensor_list_of_names_and_addresses,
                    sensor_dict_of_addresses_and_names,
                )
                
                # Filter out already accomplished sensors and organize by sensor
                for sensor_name, parsed_ln in parsed_results:
                    if sensor_name not in already_accomplished:
                        sensor_names_and_instances[sensor_name].rows.append(parsed_ln)
                        
            except Exception as e:
                print(colorize(self, 'error', f'Rust parsing failed: {e}. Falling back to Python.'))
                # Fallback to Python implementation
                parse_line__partialed = partial(parse_line_rust, already_accomplished=already_accomplished)

                with open(source_log, errors=ACTION_ON_ERROR) as lines:
                    with Pool() as pool:
                        valid_lines = pool.imap(
                            func=parse_line__partialed,
                            iterable=lines,
                            chunksize=MYSQLConfig.POOL_CHUNKSIZE.value,
                        )

                        for sensor_name_, parsed_ln_ in valid_lines:
                            if not parsed_ln_:
                                continue
                            sensor_names_and_instances[sensor_name_].rows.append(parsed_ln_)

            parse_end = perf_counter()
            parse_duration = int(parse_end - parse_start)
            print(f'parsed in {parse_duration:,} seconds ({convert_second(seconds=parse_duration, verbose=False)})')

            # Continue with database operations for each sensor
            for sensor_name, instance in sensor_names_and_instances.items():
                sensor_start = perf_counter()

                dest_dir          = f'{DaemonConfig.get_logs_parsed_dir()}/{sensor_name}/{log_date}'
                accomplished_file = f'{dest_dir}/{log_date}-accomplished.log'
                log_file          = f'{dest_dir}/{log_date}.log'

                database_name = rust_bridge.create_name_of_database(DaemonConfig.SLUG.value, log_date, sensor_name)

                ################################################

                ## remove and/or create dest_dir
                if path.exists(dest_dir):
                    should_rm_dest_dir = False

                    if force:
                        should_rm_dest_dir = True
                    else:
                        if path.exists(accomplished_file):
                            print(colorize(self, 'already_parsed', f'{command}: {log_date} for sensor {sensor_name} is already parsed. skipping'))
                            continue
                        else:
                            should_rm_dest_dir = True

                    if should_rm_dest_dir:
                        print(colorize(self, 'removing', f'removing {to_tilda(dest_dir)}'))
                        rmtree(dest_dir)
                        print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                        makedirs(dest_dir)
                else:
                    print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                    makedirs(dest_dir)

                ################################################

                ## START __inserting_into_dbs__

                ## drop/create database using Rust
                save_log(self, command, settings.HOST_NAME, log_file, f'dropping database {database_name}')
                rust_bridge.drop_database(database_name)

                save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                rust_bridge.create_database(database_name)

                ################################################
                ## *table

                if instance.no_of_rows:
                    save_log(self, command, settings.HOST_NAME, log_file, f'{instance.no_of_rows:,} rows will be inserted into database')

                    ## create table using Rust
                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {DaemonConfig.get_table_name()}')
                    rust_bridge.create_table(database_name, DaemonConfig.get_table_name(), DaemonConfig.DB_COLUMNS.value)

                    ## Use Rust for writing infiles and loading data
                    save_log(self, command, settings.HOST_NAME, log_file, 'writing infiles and loading data with Rust')
                    rust_bridge.write_infiles_and_load(instance.rows, database_name, DaemonConfig.get_table_name())

                ################################################

                # Continue with the rest of the processing (level counts, etc.)
                # This part remains in Python as it's not the bottleneck
                
                for event_type in map(itemgetter(2), instance.rows):
                    if event_type in DaemonConfig.EVENT_TYPES__CRITICALS.value:
                        instance.daemon_errors_count += 1
                    elif event_type in DaemonConfig.EVENT_TYPES__WARNINGS.value:
                        instance.daemon_warnings_count += 1

                count_rows = [
                    ('Critical', instance.daemon_errors_count),
                    ('Warning',  instance.daemon_warnings_count),
                ]

                table_name = 'levelcounttable'
                table_columns = f'''
                    ID    {MYSQLConfig.ID_DATA_TYPE.value},
                    Level {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                    Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                table_keys = 'Level,Count'
                table_marks = '%s,%s'
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                        cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                        save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(count_rows):,} rows into {table_name}')
                        cur.execute('START TRANSACTION;')
                        cur.executemany(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            count_rows
                        )
                        conn.commit()

                ################################################

                ## create accomplished file
                with open(accomplished_file, 'w') as opened:
                    opened.write('')

                sensor_end = perf_counter()
                sensor_duration = int(sensor_end - sensor_start)
                save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {sensor_duration:,} seconds ({convert_second(seconds=sensor_duration, verbose=False)})')

            source_log_end = perf_counter()
            source_log_duration = int(source_log_end - source_log_start)
            print(f'source log accomplished in {source_log_duration:,} seconds ({convert_second(seconds=source_log_duration, verbose=False)})')

        print(end_of_command_msg(self, command))
